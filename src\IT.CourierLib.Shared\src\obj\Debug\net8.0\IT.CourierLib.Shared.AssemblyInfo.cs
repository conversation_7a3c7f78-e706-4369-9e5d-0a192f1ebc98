//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("IT.CourierLib.Shared")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Interflora France")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(@"
		Interflora Library
		v 1.4.1 : Patched CreateExternalDeliveryPriceResponse
		v 1.4.0 : Added FloristIdentifier to GetLabelRequest
		v 1.3.0 : Updated GetLabelRequest
		v 1.2.0 : Added TrackingCode field to CreateExternalDeliveryResponse
		v 1.1.0 : Added GetLabelRequest and GetLabelResponse
		v 1.0.0 : First implementation
	")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.4.1+b8cc5d5cab60e847f145031919515c89b1bf5480")]
[assembly: System.Reflection.AssemblyProductAttribute("IT.CourierLib.Shared")]
[assembly: System.Reflection.AssemblyTitleAttribute("IT.CourierLib.Shared")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "The full git URL")]

// Généré par la classe MSBuild WriteCodeFragment.

